<?php
// Session management
session_start();
require_once 'includes/website-auth.php';

// Page configuration
$current_page = 'portfolio';
$page_title = 'Portfolio - Shiftur | Our Work & Projects';
$page_description = 'Explore our portfolio of web development, app development, graphic design, and AI-enhanced business solutions. See how we\'ve helped businesses achieve their digital goals.';

// Database connection is already available through website-auth.php

// Get portfolio items from database
$portfolio_items = [];
if ($conn) {
    $stmt = $conn->prepare("SELECT * FROM portfolio WHERE status = 'active' ORDER BY display_order ASC, created_at DESC");
    if ($stmt) {
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $portfolio_items[] = $row;
        }
        $stmt->close();
    }
}

// Add static design images from assets/images folder
$design_images = [
    'assets/images/ChatGPT Image Jul 15, 2025, 08_21_17 PM.png',
    'assets/images/ChatGPT Image Jul 15, 2025, 08_23_06 PM.png',
    'assets/images/ChatGPT Image Jul 15, 2025, 08_32_22 PM.png',
    'assets/images/ChatGPT Image Jul 15, 2025, 08_32_36 PM.png',
    'assets/images/ChatGPT Image Jul 15, 2025, 08_37_33 PM.png',
    'assets/images/ChatGPT Image Jul 15, 2025, 08_37_44 PM.png',
    'assets/images/ChatGPT Image Jul 15, 2025, 08_47_23 PM.png',
    'assets/images/ChatGPT Image Jul 15, 2025, 08_53_07 PM.png'
];

// Get portfolio images from portfolio folder
$portfolio_folder = 'assets/images/portfolio/';
$portfolio_files = [];
if (is_dir($portfolio_folder)) {
    $files = scandir($portfolio_folder);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && (strpos($file, '.png') !== false || strpos($file, '.jpg') !== false || strpos($file, '.jpeg') !== false)) {
            $portfolio_files[] = $portfolio_folder . $file;
        }
    }
}

// Get videos from videos folder for web development portfolio
$video_folder = 'assets/videos/';
$video_files = [];
if (is_dir($video_folder)) {
    $files = scandir($video_folder);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && (strpos($file, '.mp4') !== false || strpos($file, '.webm') !== false || strpos($file, '.mov') !== false)) {
            $video_files[] = $video_folder . $file;
        }
    }
}

// Combine all images for the collage
$all_portfolio_images = array_merge($design_images, $portfolio_files);
$all_portfolio_videos = $video_files;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1 class="page-title">Our <span class="text-purple">Portfolio</span></h1>
                <p class="page-description">
                    Discover our diverse range of projects spanning web development, mobile applications, 
                    graphic design, and AI-enhanced business solutions
                </p>
                <nav class="breadcrumb">
                    <a href="index.php">Home</a>
                    <span>/</span>
                    <span>Portfolio</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Portfolio Stats -->
    <section class="portfolio-stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Projects Completed</div>
                </div>
                <div class="stat-item" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">Client Satisfaction</div>
                </div>
                <div class="stat-item" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Happy Clients</div>
                </div>
                <div class="stat-item" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Support Available</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Filters -->
    <section class="portfolio-filters">
        <div class="container">
            <div class="filter-tabs" data-aos="fade-up">
                <button class="filter-btn active" data-filter="design">Design Portfolio</button>
                <button class="filter-btn" data-filter="web">Web Development</button>
            </div>
        </div>
    </section>

    <!-- Portfolio Grid -->
    <section class="portfolio-section">
        <div class="container">
            <div class="portfolio-grid">
                <?php if (empty($all_portfolio_images) && empty($all_portfolio_videos)): ?>
                    <div style="grid-column: 1 / -1; text-align: center; padding: 4rem 2rem; color: #6b7280;">
                        <i class="fas fa-folder-open" style="font-size: 4rem; margin-bottom: 2rem; opacity: 0.5;"></i>
                        <h3>No Portfolio Items Yet</h3>
                        <p>We're working on adding our latest projects. Check back soon!</p>
                    </div>
                <?php else: ?>
                    <!-- Design Portfolio Images -->
                    <?php foreach ($all_portfolio_images as $index => $image_path): ?>
                        <div class="portfolio-item" data-category="design" data-aos="fade-up" data-aos-delay="<?php echo ($index % 4) * 50 + 100; ?>">
                            <div class="portfolio-image">
                                <img src="<?php echo htmlspecialchars($image_path); ?>" alt="Portfolio Design <?php echo $index + 1; ?>" class="portfolio-main-image">
                                <div class="portfolio-overlay">
                                    <div class="portfolio-actions">
                                        <button class="portfolio-btn" onclick="openImageModal('<?php echo htmlspecialchars($image_path); ?>')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="portfolio-btn" onclick="downloadImage('<?php echo htmlspecialchars($image_path); ?>')">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Web Development Portfolio Videos -->
                    <?php foreach ($all_portfolio_videos as $index => $video_path): ?>
                        <div class="portfolio-item video-item" data-category="web" data-aos="fade-up" data-aos-delay="<?php echo ($index % 4) * 50 + 100; ?>">
                            <div class="portfolio-image">
                                <video class="portfolio-main-video" muted loop preload="metadata">
                                    <source src="<?php echo htmlspecialchars($video_path); ?>" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <div class="modal-content">
            <span class="close-modal" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" src="" alt="Portfolio Image">
        </div>
    </div>

    <!-- Video Modal -->
    <div id="videoModal" class="image-modal" onclick="closeVideoModal()">
        <div class="modal-content">
            <span class="close-modal" onclick="closeVideoModal()">&times;</span>
            <video id="modalVideo" controls autoplay muted>
                <source src="" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
    </div>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <div class="cta-content" data-aos="fade-up">
                <h2 class="cta-title">Ready to Start Your Project?</h2>
                <p class="cta-description">
                    Let's discuss how we can help bring your vision to life with our expertise
                </p>
                <div class="cta-buttons">
                    <a href="contact.php" class="btn btn-primary btn-large">
                        <span>Get Started</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="about.php" class="btn btn-outline btn-large">
                        <span>Learn More</span>
                        <i class="fas fa-info-circle"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="assets/js/main.js"></script>

    <script>
        // Image Modal Functions
        function openImageModal(imageSrc) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageSrc;
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function downloadImage(imageSrc) {
            const link = document.createElement('a');
            link.href = imageSrc;
            link.download = imageSrc.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Video Modal Functions
        function openVideoModal(videoSrc) {
            const modal = document.getElementById('videoModal');
            const modalVideo = document.getElementById('modalVideo');
            modalVideo.src = videoSrc;
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const modalVideo = document.getElementById('modalVideo');
            modal.style.display = 'none';
            modalVideo.pause();
            modalVideo.currentTime = 0;
            document.body.style.overflow = 'auto';
        }

        function downloadVideo(videoSrc) {
            const link = document.createElement('a');
            link.href = videoSrc;
            link.download = videoSrc.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
                closeVideoModal();
            }
        });

        // Portfolio Filter Functions
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtns = document.querySelectorAll('.filter-btn');
            const portfolioItems = document.querySelectorAll('.portfolio-item');

            // Initialize - show only design items by default (since Design Portfolio is active)
            portfolioItems.forEach(item => {
                if (item.getAttribute('data-category') === 'design') {
                    // For masonry layout, use inline-block instead of block on mobile
                    if (window.innerWidth <= 768) {
                        item.style.display = 'inline-block';
                    } else {
                        item.style.display = 'block';
                    }
                    item.style.width = '100%';
                } else {
                    item.style.display = 'none';
                }
            });

            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterBtns.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');

                    const filter = this.getAttribute('data-filter');

                    portfolioItems.forEach(item => {
                        if (item.getAttribute('data-category') === filter) {
                            // For masonry layout, use inline-block instead of block
                            if (window.innerWidth <= 768) {
                                item.style.display = 'inline-block';
                            } else {
                                item.style.display = 'block';
                            }
                            item.style.width = '100%';
                        } else {
                            item.style.display = 'none';
                        }
                    });

                    // Force reflow for masonry layout on mobile
                    if (window.innerWidth <= 768) {
                        const portfolioGrid = document.querySelector('.portfolio-grid');
                        if (portfolioGrid) {
                            portfolioGrid.style.columns = portfolioGrid.style.columns;
                        }
                    }
                });
            });

            // Video Hover to Play Functionality
            const videoItems = document.querySelectorAll('.video-item');

            videoItems.forEach(item => {
                const video = item.querySelector('.portfolio-main-video');

                if (video) {
                    item.addEventListener('mouseenter', function() {
                        video.play();
                    });

                    item.addEventListener('mouseleave', function() {
                        video.pause();
                        video.currentTime = 0;
                    });
                }
            });

            // Handle window resize for responsive behavior
            window.addEventListener('resize', function() {
                const activeFilter = document.querySelector('.filter-btn.active');
                if (activeFilter) {
                    const filter = activeFilter.getAttribute('data-filter');
                    portfolioItems.forEach(item => {
                        if (item.getAttribute('data-category') === filter && item.style.display !== 'none') {
                            // Update display property based on screen size
                            if (window.innerWidth <= 768) {
                                item.style.display = 'inline-block';
                            } else {
                                item.style.display = 'block';
                            }
                            item.style.width = '100%';
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
