<?php
// Determine if we're in a service page or admin panel
$is_service_page = strpos($_SERVER['REQUEST_URI'], '/services/') !== false;
$is_admin_page = strpos($_SERVER['REQUEST_URI'], '/admin/') !== false;

// Set base path
$base_path = '';
if ($is_service_page) {
    $base_path = '../';
} elseif ($is_admin_page) {
    $base_path = '../';
}
?>

<header class="header">
    <div class="container">
        <div class="header-content">
            <!-- Logo -->
            <a href="<?php echo $base_path; ?>index.php" class="logo">
                <img src="<?php echo $base_path; ?>assets/images/logo.png" alt="Shiftur" class="logo-img">
            </a>

            <!-- Navigation Menu -->
            <nav class="nav-menu">
                <ul>
                    <li>
                        <a href="<?php echo $base_path; ?>index.php" class="nav-link <?php echo (isset($current_page) && $current_page == 'home') ? 'active' : ''; ?>">
                            Home
                        </a>
                    </li>
                  
                    <li class="nav-dropdown">
                        <a href="#" class="nav-link">
                            Services <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="<?php echo $base_path; ?>services/web-app-development.php" class="dropdown-link">
                                <i class="fas fa-code"></i>
                                <div>
                                    <span>Web & App Development</span>
                                    <small>Custom websites and mobile apps</small>
                                </div>
                            </a>
                            <a href="<?php echo $base_path; ?>services/digital-marketing.php" class="dropdown-link">
                                <i class="fas fa-bullhorn"></i>
                                <div>
                                    <span>Digital Marketing</span>
                                    <small>SEO, PPC, and social media</small>
                                </div>
                            </a>
                            <a href="<?php echo $base_path; ?>services/ai-business-solutions.php" class="dropdown-link">
                                <i class="fas fa-robot"></i>
                                <div>
                                    <span>AI Business Solutions</span>
                                    <small>Automation and analytics</small>
                                </div>
                            </a>
                            <a href="<?php echo $base_path; ?>services/graphic-design.php" class="dropdown-link">
                                <i class="fas fa-palette"></i>
                                <div>
                                    <span>Graphic Design</span>
                                    <small>Branding and visual identity</small>
                                </div>
                            </a>
                        </div>
                    </li>
                    <li class="nav-dropdown amazon-mega-menu">
                        <a href="<?php echo $base_path; ?>services/amazon-services.php" class="nav-link">
                            Amazon <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="amazon-mega-dropdown">
                            <div class="mega-menu-container">
                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">GETTING STARTED</h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo $base_path; ?>services/how-fba-works.php">How FBA Works</a></li>


                                        <li><a href="<?php echo $base_path; ?>services/choosing-business-model.php">Choosing Your Business Model</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/understanding-amazon-fees.php">Understanding Amazon Fees</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-seller-account-setup.php">Seller Account Setup Guide</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">STRATEGY & LAUNCH</h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#private-label-service">Full-Service Private Label</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#product-research">Product Research & Discovery</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#supplier-sourcing">Supplier & Sourcing</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-brand-registry.php">Trademark & Brand Registry</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">MARKETING & GROWTH</h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo $base_path; ?>services/amazon-ads-management.php">Amazon PPC Management</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#listing-optimization">Listing Optimization (SEO & Copy)</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#product-photography">Product Photography & A+ Content</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-account-health.php">Ongoing Account Health</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">OPERATIONS & LOGISTICS</h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#fulfillment-strategy">Fulfillment Strategy (FBA, FBM, SFP)</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#fba-prep">FBA Prep & Management</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#inbound-shipping">Inbound Shipping (LTL & FTL)</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#inventory-planning">Inventory Planning & Management</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#multi-channel">Multi-Channel Fulfillment (MCF)</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#warehouse-coordination">3PL & Warehouse Coordination</a></li>
                                        <li><a href="<?php echo $base_path; ?>services/amazon-services.php#returns-management">Returns Management (Reverse Logistics)</a></li>
                                    </ul>
                                </div>

                            </div>
                        </div>
                    </li>
                      <li>
                        <a href="<?php echo $base_path; ?>about.php" class="nav-link <?php echo (isset($current_page) && $current_page == 'about') ? 'active' : ''; ?>">
                            About
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo $base_path; ?>portfolio.php" class="nav-link <?php echo (isset($current_page) && $current_page == 'portfolio') ? 'active' : ''; ?>">
                            Portfolio
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo $base_path; ?>contact.php" class="nav-link <?php echo (isset($current_page) && $current_page == 'contact') ? 'active' : ''; ?>">
                            Contact
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Navigation Actionu have add charts but s -->
            <div class="nav-actions">
                <?php if (isset($_SESSION['website_user_id'])): ?>
                    <!-- Get Quote Button (For Logged-in Users) -->
                    <a href="<?php echo $base_path; ?>contact.php" class="btn btn-primary">
                        <i class="fas fa-quote-left"></i>
                        <span>Get Quote</span>
                    </a>

                    <!-- User Menu -->
                    <div class="user-menu">
                        <div class="user-avatar-small">
                            <?php echo strtoupper(substr($_SESSION['website_user_name'] ?? 'U', 0, 1)); ?>
                        </div>
                        <div class="user-dropdown">
                            <button class="user-dropdown-toggle">
                                <span><?php echo htmlspecialchars(explode(' ', $_SESSION['website_user_name'] ?? 'User')[0]); ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="user-dropdown-menu">
                                <a href="<?php echo $base_path; ?>profile.php" class="user-dropdown-link">
                                    <i class="fas fa-user"></i>
                                    My Profile
                                </a>
                                <a href="<?php echo $base_path; ?>portfolio.php" class="user-dropdown-link">
                                    <i class="fas fa-folder"></i>
                                    Our Work
                                </a>
                                <div class="user-dropdown-divider"></div>
                                <a href="<?php echo $base_path; ?>logout.php" class="user-dropdown-link">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Guest Actions -->
                    <div class="auth-actions">
                        <a href="<?php echo $base_path; ?>login.php" class="btn btn-outline">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Login</span>
                        </a>
                        <a href="<?php echo $base_path; ?>contact.php" class="btn btn-primary">
                            <i class="fas fa-quote-left"></i>
                            <span>Get Quote</span>
                        </a>
                    </div>
                <?php endif; ?>

                <button class="mobile-menu-toggle" id="mobileMenuToggle" onclick="toggleMobileMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <button class="mobile-menu-close" id="mobileMenuClose">
            <i class="fas fa-times"></i>
        </button>
        <div class="mobile-menu-content">
            <a href="<?php echo $base_path; ?>index.php" class="mobile-nav-link">Home</a>
            <a href="<?php echo $base_path; ?>about.php" class="mobile-nav-link">About</a>
            
            <div class="mobile-dropdown">
                <button class="mobile-dropdown-toggle">
                    Services <i class="fas fa-chevron-down"></i>
                </button>
                <div class="mobile-dropdown-content">
                    <a href="<?php echo $base_path; ?>services/web-app-development.php" class="mobile-nav-link">Web & App Development</a>
                    <a href="<?php echo $base_path; ?>services/digital-marketing.php" class="mobile-nav-link">Digital Marketing</a>
                    <a href="<?php echo $base_path; ?>services/ai-business-solutions.php" class="mobile-nav-link">AI Business Solutions</a>
                    <a href="<?php echo $base_path; ?>services/graphic-design.php" class="mobile-nav-link">Graphic Design</a>
                </div>
            </div>

            <div class="mobile-dropdown">
                <button class="mobile-dropdown-toggle">
                    Amazon Services <i class="fas fa-chevron-down"></i>
                </button>
                <div class="mobile-dropdown-content">
                    <!-- Getting Started -->
                    <div class="mobile-submenu-header">Getting Started</div>
                    <a href="<?php echo $base_path; ?>services/how-fba-works.php" class="mobile-nav-link">How FBA Works</a>
                    <a href="<?php echo $base_path; ?>services/choosing-business-model.php" class="mobile-nav-link">A-Z Private Label</a>
                    <a href="<?php echo $base_path; ?>services/choosing-business-model.php" class="mobile-nav-link">Choosing Your Business Model</a>
                    <a href="<?php echo $base_path; ?>services/understanding-amazon-fees.php" class="mobile-nav-link">Understanding Amazon Fees</a>
                    <a href="<?php echo $base_path; ?>services/amazon-seller-account-setup.php" class="mobile-nav-link">Seller Account Setup Guide</a>

                    <!-- Strategy & Launch -->
                    <div class="mobile-submenu-header">Strategy & Launch</div>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#private-label-service" class="mobile-nav-link">Full-Service Private Label</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#product-research" class="mobile-nav-link">Product Research & Discovery</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#supplier-sourcing" class="mobile-nav-link">Supplier & Sourcing</a>
                    <a href="<?php echo $base_path; ?>services/amazon-brand-registry.php" class="mobile-nav-link">Trademark & Brand Registry</a>

                    <!-- Marketing & Growth -->
                    <div class="mobile-submenu-header">Marketing & Growth</div>
                    <a href="<?php echo $base_path; ?>services/amazon-ads-management.php" class="mobile-nav-link">Amazon PPC Management</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#listing-optimization" class="mobile-nav-link">Listing Optimization (SEO & Copy)</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#product-photography" class="mobile-nav-link">Product Photography & A+ Content</a>
                    <a href="<?php echo $base_path; ?>services/amazon-account-health.php" class="mobile-nav-link">Ongoing Account Health</a>

                    <!-- Operations & Logistics -->
                    <div class="mobile-submenu-header">Operations & Logistics</div>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#fulfillment-strategy" class="mobile-nav-link">Fulfillment Strategy (FBA, FBM, SFP)</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#fba-prep" class="mobile-nav-link">FBA Prep & Management</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#inbound-shipping" class="mobile-nav-link">Inbound Shipping (LTL & FTL)</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#inventory-planning" class="mobile-nav-link">Inventory Planning & Management</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#multi-channel" class="mobile-nav-link">Multi-Channel Fulfillment (MCF)</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#warehouse-coordination" class="mobile-nav-link">3PL & Warehouse Coordination</a>
                    <a href="<?php echo $base_path; ?>services/amazon-services.php#returns-management" class="mobile-nav-link">Returns Management (Reverse Logistics)</a>
                </div>
            </div>
            
            <a href="<?php echo $base_path; ?>portfolio.php" class="mobile-nav-link">Portfolio</a>
            <a href="<?php echo $base_path; ?>contact.php" class="mobile-nav-link">Contact</a>
            
            <div class="mobile-menu-actions">
                <?php if (isset($_SESSION['website_user_id'])): ?>
                    <!-- Logged in user actions -->
                    <a href="<?php echo $base_path; ?>profile.php" class="mobile-nav-link">
                        <i class="fas fa-user"></i>
                        My Profile
                    </a>
                    <a href="<?php echo $base_path; ?>logout.php" class="mobile-nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                    <a href="<?php echo $base_path; ?>contact.php" class="btn btn-primary btn-full">
                        <i class="fas fa-quote-left"></i>
                        <span>Get Quote</span>
                    </a>
                <?php else: ?>
                    <!-- Guest actions -->
                    <a href="<?php echo $base_path; ?>login.php" class="btn btn-outline btn-full">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                    <a href="<?php echo $base_path; ?>contact.php" class="btn btn-primary btn-full">
                        <i class="fas fa-quote-left"></i>
                        <span>Get Quote</span>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</header>

<!-- Under Construction Notice -->
<div class="construction-notice">
    <div class="container">
        <div class="construction-content">
            <i class="fas fa-tools"></i>
            <span>Our website is under construction - Stay tuned for updates!</span>
        </div>
    </div>
</div>

<style>
/* Under Construction Notice */
.construction-notice {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 12px 0;
    text-align: center;
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 90px; /* Push it below the fixed header */
}

.construction-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: 600;
    font-size: 0.95rem;
}

.construction-content i {
    font-size: 1.1rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

@media (max-width: 768px) {
    .construction-notice {
        padding: 10px 0;
        margin-top: 70px; /* Adjust for smaller header on mobile */
    }

    .construction-content {
        font-size: 0.85rem;
        gap: 8px;
    }
}

/* Header Dropdown Styles */
.nav-dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    z-index: 1000;
    margin-top: 0.5rem;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-normal);
    border-bottom: 1px solid var(--border-primary);
}

.dropdown-link:last-child {
    border-bottom: none;
}

.dropdown-link:hover {
    background-color: var(--bg-hover);
    color: var(--purple-primary);
}

.dropdown-link i {
    font-size: 1.2rem;
    color: var(--purple-primary);
    width: 20px;
    text-align: center;
}

.dropdown-link div span {
    display: block;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.dropdown-link div small {
    display: block;
    color: var(--text-muted);
    font-size: 0.8rem;
}





/* Mobile Menu Styles */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 10001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: var(--transition-normal);
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-card);
    z-index: 10000;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    overflow-y: auto;
}

.mobile-menu.active {
    transform: translateX(0);
}

.mobile-menu-content {
    padding: 2rem;
}

.mobile-nav-link {
    display: block;
    padding: 1rem 0;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid var(--border-primary);
    transition: var(--transition-normal);
}

.mobile-nav-link:hover {
    color: var(--purple-primary);
}

.mobile-dropdown-toggle {
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    padding: 1rem 0;
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-normal);
}

.mobile-dropdown-toggle:hover {
    color: var(--purple-primary);
}



.mobile-menu-actions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-primary);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* User Menu Styles */
.user-menu {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.user-avatar-small {
    width: 35px;
    height: 35px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.user-dropdown {
    position: relative;
}

.user-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
}

.user-dropdown-toggle:hover {
    background: var(--bg-hover);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    z-index: 1000;
}

.user-dropdown:hover .user-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-normal);
    border-bottom: 1px solid var(--border-primary);
}

.user-dropdown-link:last-child {
    border-bottom: none;
}

.user-dropdown-link:hover {
    background: var(--bg-hover);
    color: var(--purple-primary);
}

.user-dropdown-divider {
    height: 1px;
    background: var(--border-primary);
    margin: 0.5rem 0;
}

/* Auth Actions */
.auth-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.auth-actions .btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Mobile Submenu Headers */
.mobile-submenu-header {
    font-weight: 700;
    color: var(--purple-primary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1.25rem 0 0.75rem 0;
    margin-top: 1.5rem;
    border-top: 2px solid var(--purple-primary);
    background: rgba(139, 92, 246, 0.1);
    margin-left: -1rem;
    margin-right: -1rem;
    padding-left: 1rem;
    border-radius: 4px;
}

.mobile-submenu-header:first-child {
    margin-top: 0.5rem;
    border-top: 2px solid var(--purple-primary);
}

/* Mobile nav links in dropdown */
.mobile-dropdown-content .mobile-nav-link {
    padding: 0.75rem 0;
    font-size: 0.9rem;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
    display: block;
    transition: var(--transition-normal);
}

.mobile-dropdown-content .mobile-nav-link:hover {
    color: var(--purple-primary);
    padding-left: 0.5rem;
}

/* Improve mobile dropdown content */
.mobile-dropdown-content {
    padding-left: 1rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
    background: var(--bg-primary);
}

.mobile-dropdown.active .mobile-dropdown-content {
    max-height: 1200px; /* Increased for all Amazon services */
    overflow-y: auto;
}

/* Mobile dropdown scrolling for very long lists */
@media (max-height: 600px) {
    .mobile-dropdown.active .mobile-dropdown-content {
        max-height: 400px;
        overflow-y: scroll;
    }
}



@media (max-width: 768px) {
    .nav-menu {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }

    .mobile-menu {
        display: block !important;
    }

    .user-menu,
    .auth-actions {
        display: none !important;
    }

    .header-content {
        padding: 1rem;
    }

    .logo-img {
        height: 35px;
    }
}
</style>

<script>
// Simple mobile menu toggle function
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');

    if (mobileMenu && mobileMenuToggle) {
        if (mobileMenu.classList.contains('active')) {
            // Close menu
            mobileMenu.classList.remove('active');
            mobileMenuToggle.classList.remove('active');
            mobileMenu.style.transform = 'translateX(-100%)';
            document.body.style.overflow = '';
        } else {
            // Open menu
            mobileMenu.classList.add('active');
            mobileMenuToggle.classList.add('active');
            mobileMenu.style.transform = 'translateX(0)';
            document.body.style.overflow = 'hidden';
        }
    }
}

// Close mobile menu when clicking close button
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuClose = document.getElementById('mobileMenuClose');
    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', function() {
            toggleMobileMenu();
        });
    }
});
</script>
